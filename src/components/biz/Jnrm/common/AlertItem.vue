<template>
	<div
		class="flex flex-col rounded-lg p-2 transition-all duration-200"
		:class="{
			// 未处理告警的样式 - 使用更柔和的红色和橙色
			'bg-rose-500/15 border-rose-400/40 border shadow-sm': alert.level === '严重' && !alert.handleStatus,
			'bg-amber-500/15 border-amber-400/40 border shadow-sm': alert.level === '警告' && !alert.handleStatus,
			// 已处理告警的样式（灰色、半透明）
			'bg-gray/10 border-gray/20 border opacity-60': alert.handleStatus,
		}"
	>
		<div class="mb-1 flex items-start justify-between">
			<div class="flex items-center">
				<div
					class="mr-1 h-2 w-2 rounded-full"
					:class="{
						// 未处理告警的颜色 - 使用更鲜艳的指示器颜色
						'bg-rose-500': alert.level === '严重' && !alert.handleStatus,
						'bg-amber-500': alert.level === '警告' && !alert.handleStatus,
						// 已处理告警的颜色
						'bg-gray': alert.handleStatus,
					}"
				></div>
				<span
					class="font-medium text-sm"
					:class="{
						// 未处理告警的颜色 - 使用更深的文字颜色以提高可读性
						'text-rose-600': alert.level === '严重' && !alert.handleStatus,
						'text-amber-600': alert.level === '警告' && !alert.handleStatus,
						// 已处理告警的颜色
						'text-gray': alert.handleStatus,
					}"
				>
					{{ alert.alertType }}
					<span v-if="alert.handleStatus" class="ml-1 text-gray-400 text-xs">(已处理)</span>
				</span>
			</div>
			<span
				class="rounded px-1.5 py-0.5 font-medium text-xs"
				:class="{
					// 未处理告警的样式 - 使用更深色的背景让严重标签更突出
					'bg-rose-500/20 text-rose-200 border border-rose-400/30': alert.level === '严重' && !alert.handleStatus,
					'bg-amber-100 text-amber-700 border border-amber-200': alert.level === '警告' && !alert.handleStatus,
					// 已处理告警的样式
					'bg-gray/20 text-gray': alert.handleStatus,
				}"
			>
				{{ alert.level === '严重' ? '严重' : '警告' }}
			</span>
		</div>
		<p class="mb-1 text-sm">{{ alert.alertContent }}</p>
		<div class="flex items-center justify-between text-gray-400 text-sm">
			<span>{{ X_DATE_UTILS.formatDate(alert.alertTime) }}</span>
			<!-- 只有未处理的告警才显示操作按钮 -->
			<div v-if="showActions && !alert.handleStatus" class="flex space-x-2">
				<button class="text-white hover:text-secondary" @click="handleLocate(alert.transportOrderId)">定位</button>
				<button class="text-white hover:text-secondary" @click="$emit('handle', alert)">处理</button>
			</div>
			<!-- 已处理的告警显示处理时间 -->
			<div v-else-if="alert.handleStatus && alert.handleTime" class="text-gray-500 text-xs">
				已处理：{{ X_DATE_UTILS.formatDate(alert.handleTime) }}
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
interface Alert {
	id: string
	alertType: string
	level: 'danger' | 'warning'
	alertContent: string
	alertTime: string
	transportOrderId: number
	licensePlate?: string
	handleStatus?: boolean
	handleTime?: string
	handleMethod?: string
}

interface Props {
	alert: Alert
	showActions?: boolean
}
const transportOrderStore = useTransportOrderStore()
interface Emits {
	(e: 'locate', alert: Alert): void
	(e: 'handle', alert: Alert): void
}

const props = withDefaults(defineProps<Props>(), {
	showActions: true,
})
function handleLocate(id) {
	// 选中运单
	transportOrderStore.selectOrder(id)
}
defineEmits<Emits>()
</script>

<style scoped></style>
